/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #2C3E50;
    direction: rtl;
    text-align: right;
    /* Improved base font size with fluid scaling */
    font-size: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 clamp(15px, 2vw, 20px);
}

/* Header Styles */
.header {
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-brand .logo {
    width: 60px;
    height: 60px;
    object-fit: cover;
    transition: all 0.3s ease;
    display: block !important;
    position: relative;
}

.nav-brand .logo[src=""],
.nav-brand .logo:not([src]) {
    background: linear-gradient(135deg, #F39C12, #E67E22);
}

.nav-brand .logo[src=""]:before,
.nav-brand .logo:not([src]):before {
    opacity: 1;
}

.nav-brand h2 {
    color: #F39C12;
    font-weight: 700;
    font-size: clamp(1.1rem, 1rem + 0.5vw, 1.25rem);
    line-height: 1.2;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #BDC3C7;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #F39C12;
    background: rgba(243, 156, 18, 0.1);
    transform: translateY(-2px);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #F39C12;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hamburger Animation */
.hamburger.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active .bar:nth-child(2) {
    opacity: 0;
}

.hamburger.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Hero Section */
.hero {
    height: 100vh;
    overflow: hidden;
    position: relative;
    padding: 0 !important;
}

.slider {
    position: relative;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(44, 62, 80, 0.8), rgba(52, 73, 94, 0.6));
}

.slide-content {
    position: relative;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 2rem;
    animation: slideUp 1s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-title {
    font-size: clamp(2rem, 2rem + 3vw, 3.5rem);
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.slide-description {
    font-size: clamp(1rem, 0.9rem + 0.8vw, 1.3rem);
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.5;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(45deg, #F39C12, #E67E22);
    color: white;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 2rem;
}

.slider-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.slider-btn:hover {
    background: rgba(243, 156, 18, 0.8);
    transform: scale(1.1);
}

.slider-dots {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #F39C12;
    transform: scale(1.2);
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: clamp(1.75rem, 1.5rem + 2vw, 2.5rem);
    font-weight: 700;
    color: #2C3E50;
    margin-bottom: 1rem;
    position: relative;
    line-height: 1.2;
    letter-spacing: -0.01em;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #F39C12, #E67E22);
    border-radius: 2px;
}

.section-subtitle {
    font-size: clamp(1rem, 0.9rem + 0.6vw, 1.2rem);
    color: #7F8C8D;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Services Section */
.services {
    background: #F8F9FA;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.25rem;
}

.service-card {
    background: white;
    padding: 1.25rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(45deg, #F39C12, #E67E22);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #F39C12, #E67E22);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: rotate(360deg);
}

.service-icon i {
    font-size: 2rem;
    color: white;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2C3E50;
}

.service-card p {
    color: #7F8C8D;
    line-height: 1.6;
}

/* Gallery Section */
.gallery {
    background: white;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.gallery-card {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    background: white;
}

.gallery-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.gallery-image {
    position: relative;
    overflow: hidden;
    height: 300px;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
}

.gallery-card:hover .gallery-image img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.9), rgba(44, 62, 80, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
}

.gallery-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-content {
    text-align: center;
    color: white;
    padding: 2rem;
    transform: translateY(20px);
    transition: all 0.4s ease;
}

.gallery-card:hover .gallery-content {
    transform: translateY(0);
}

.gallery-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
}

.gallery-content p {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    line-height: 1.6;
}

.gallery-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.gallery-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    color: white;
}

.gallery-btn i {
    font-size: 0.9rem;
}

/* Blog Section */
.blog {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 5rem 0;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.blog-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.blog-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.blog-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
}

.blog-card:hover .blog-image img {
    transform: scale(1.1);
}

.blog-category {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(45deg, #F39C12, #E67E22);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.blog-content {
    padding: 1.5rem;
}

.blog-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #7F8C8D;
}

.blog-meta span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.blog-meta i {
    color: #F39C12;
}

.blog-content h3 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2C3E50;
    margin-bottom: 1rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-content p {
    color: #7F8C8D;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #F39C12;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.blog-btn:hover {
    color: #E67E22;
    transform: translateX(-5px);
}

.blog-btn i {
    transition: all 0.3s ease;
}

.blog-btn:hover i {
    transform: translateX(-3px);
}

.blog-footer {
    text-align: center;
    margin-top: 2rem;
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2C3E50;
    margin-bottom: 1.5rem;
}

.about-text p {
    color: #7F8C8D;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat {
    text-align: center;
}

.stat h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #F39C12;
    margin-bottom: 0.5rem;
}

.stat p {
    color: #7F8C8D;
    font-weight: 600;
}

.about-image {
    position: relative;
}

.about-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.about-image img:hover {
    transform: scale(1.05);
}

/* Portfolio Section */
.portfolio {
    background: #F8F9FA;
}

.portfolio-filter {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.8rem 1.5rem;
    background: white;
    border: 2px solid #E74C3C;
    color: #E74C3C;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.filter-btn.active,
.filter-btn:hover {
    background: #E74C3C;
    color: white;
    transform: translateY(-2px);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.portfolio-item {
    cursor: pointer;
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    height: 250px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.portfolio-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.portfolio-item:hover img {
    transform: scale(1.1);
}

.portfolio-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(44, 62, 80, 0.9));
    color: white;
    padding: 2rem;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    transform: translateY(0);
}

.portfolio-overlay h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.portfolio-overlay p {
    opacity: 0.9;
    font-size: 0.9rem;
}

/* Contact Section */
.contact {
    background: #2C3E50;
    color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #F39C12;
}

.contact-info p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-item i {
    width: 50px;
    height: 50px;
    background: #F39C12;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.contact-item h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.contact-item p {
    opacity: 0.8;
    margin: 0;
}

.contact-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1) !important;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    direction: rtl;
    text-align: right;
    font-family: 'Cairo', sans-serif;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.9);
    opacity: 1;
    direction: rtl;
    text-align: right;
    font-weight: 400;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.2);
}

/* Enhanced placeholder styling for better visibility and RTL support */
.form-group input::-webkit-input-placeholder,
.form-group textarea::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.9);
    opacity: 1;
    direction: rtl;
    text-align: right;
    font-weight: 400;
}

.form-group input::-moz-placeholder,
.form-group textarea::-moz-placeholder {
    color: rgba(255, 255, 255, 0.9);
    opacity: 1;
    direction: rtl;
    text-align: right;
    font-weight: 400;
}

.form-group input:-ms-input-placeholder,
.form-group textarea:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.9);
    opacity: 1;
    direction: rtl;
    text-align: right;
    font-weight: 400;
}

.form-group input:-moz-placeholder,
.form-group textarea:-moz-placeholder {
    color: rgba(255, 255, 255, 0.9);
    opacity: 1;
    direction: rtl;
    text-align: right;
    font-weight: 400;
}

/* Map Section */
.map {
    height: 400px;
    padding: 0 !important;
}

.map-container {
    height: 100%;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Footer */
.footer {
    background: #1A1A1A;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: #F39C12;
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section p {
    opacity: 0.8;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-section ul li a:hover {
    color: #F39C12;
    padding-right: 5px;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #F39C12;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #E67E22;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* Page Header */
.page-header {
    background: linear-gradient(45deg, #2C3E50, #34495E);
    color: white;
    padding: 8rem 0 6rem;
    text-align: center;
    margin-top: 50px;
}

.page-header h1 {
    font-size: clamp(2rem, 1.5rem + 2.5vw, 3rem);
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.page-header p {
    font-size: clamp(1rem, 0.9rem + 0.6vw, 1.2rem);
    opacity: 0.9;
    line-height: 1.5;
}

/* Services Details */
.services-details {
    padding: 5rem 0;
}

.service-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 5rem;
}

.service-detail.reverse {
    direction: ltr;
}

.service-detail.reverse .service-content {
    direction: rtl;
}

.service-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.service-content h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #2C3E50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.service-content h2 i {
    color: #F39C12;
}

.service-content p {
    color: #7F8C8D;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.8;
}

.service-content ul {
    list-style: none;
}

.service-content ul li {
    padding: 0.5rem 0;
    color: #7F8C8D;
    position: relative;
    padding-right: 2rem;
}

.service-content ul li::before {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 0;
    color: #F39C12;
}

/* Why Choose Us */
.why-choose-us {
    background: #F8F9FA;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.feature-card i {
    font-size: 3rem;
    color: #F39C12;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2C3E50;
}

.feature-card p {
    color: #7F8C8D;
}

/* Portfolio Page */
.portfolio-page {
    padding: 5rem 0;
}

.portfolio-grid-extended {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.portfolio-details {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

.portfolio-details span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Process Section */
.process {
    background: #F8F9FA;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #F39C12, #E67E22);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.step h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2C3E50;
}

.step p {
    color: #7F8C8D;
}

/* About Page */
.about-page {
    padding: 5rem 0;
}

.about-intro {
    text-align: center;
    margin-bottom: 4rem;
}

.about-intro h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2C3E50;
    margin-bottom: 1.5rem;
}

.about-intro p {
    font-size: 1.2rem;
    color: #7F8C8D;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

.values-list {
    list-style: none;
    margin-top: 2rem;
}

.values-list li {
    padding: 1rem 0;
    border-bottom: 1px solid #E8E8E8;
    color: #7F8C8D;
    line-height: 1.6;
}

.values-list li:last-child {
    border-bottom: none;
}

.values-list strong {
    color: #2C3E50;
    font-weight: 600;
}

/* Team Section */
.team {
    background: #F8F9FA;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.team-member {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.member-image {
    position: relative;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.team-member:hover .member-image img {
    transform: scale(1.1);
}

.member-info {
    padding: 2rem;
    text-align: center;
}

.member-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2C3E50;
    margin-bottom: 0.5rem;
}

.member-info p {
    color: #F39C12;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.member-info span {
    color: #7F8C8D;
    font-size: 0.9rem;
}

/* Statistics Section */
.statistics {
    background: linear-gradient(45deg, #2C3E50, #34495E);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
}

.stat-icon {
    font-size: 3rem;
    color: #F39C12;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.stat-item:hover .stat-icon {
    transform: scale(1.2);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #F39C12;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Certificates Section */
.certificates {
    background: #F8F9FA;
}

.certificates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.certificate {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.certificate:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.certificate i {
    font-size: 3rem;
    color: #F39C12;
    margin-bottom: 1.5rem;
}

.certificate h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2C3E50;
    margin-bottom: 1rem;
}

.certificate p {
    color: #7F8C8D;
}

/* Contact Page */
.contact-page {
    padding: 5rem 0;
}

.contact-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info-section h2 {
    font-size: clamp(1.5rem, 1.3rem + 1.8vw, 2.2rem);
    font-weight: 700;
    color: #2C3E50;
    margin-bottom: 1rem;
    line-height: 1.2;
    letter-spacing: -0.01em;
}

.contact-info-section p {
    color: #7F8C8D;
    margin-bottom: 2rem;
    font-size: clamp(1rem, 0.9rem + 0.5vw, 1.1rem);
    line-height: 1.7;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
    background: #F8F9FA;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.contact-method:hover {
    background: white;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transform: translateY(-3px);
}

.method-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #F39C12, #E67E22);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.method-info h3 {
    font-size: clamp(1.1rem, 1rem + 0.4vw, 1.2rem);
    font-weight: 600;
    color: #2C3E50;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.method-info p {
    color: #7F8C8D;
    margin: 0.25rem 0;
    font-size: clamp(0.9rem, 0.85rem + 0.25vw, 1rem);
    line-height: 1.5;
}

.method-info span {
    color: #F39C12;
    font-size: clamp(0.85rem, 0.8rem + 0.25vw, 0.9rem);
    font-weight: 600;
}

.contact-form-extended {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.contact-form-extended h2 {
    font-size: clamp(1.4rem, 1.2rem + 1.6vw, 2rem);
    font-weight: 700;
    color: #2C3E50;
    margin-bottom: 2rem;
    text-align: center;
    line-height: 1.2;
    letter-spacing: -0.01em;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2C3E50;
    font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #E8E8E8;
    border-radius: 10px;
    font-size: 1rem;
    color: #fff;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #F39C12;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

.checkbox-group label {
    margin-bottom: 0;
    font-weight: 400;
}

.checkbox-group a {
    color: #F39C12;
    text-decoration: none;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

/* FAQ Section */
.faq {
    background: #F8F9FA;
}

.faq-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.faq-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.faq-question {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: #F8F9FA;
}

.faq-question h3 {
    font-size: clamp(1.1rem, 1rem + 0.4vw, 1.2rem);
    font-weight: 600;
    color: #2C3E50;
    line-height: 1.4;
}

.faq-question i {
    color: #F39C12;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    color: #7F8C8D;
    line-height: 1.6;
}

/* Map Section */
.map-section {
    padding: 5rem 0;
    background: #F8F9FA;
}

.map-section h2 {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    color: #2C3E50;
    margin-bottom: 2rem;
}

/* Blog Page */
.blog-page {
    padding: 5rem 0;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.blog-post {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.blog-post:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.post-image {
    position: relative;
    overflow: hidden;
}

.post-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.blog-post:hover .post-image img {
    transform: scale(1.1);
}

.post-category {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #F39C12;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.post-content {
    padding: 2rem;
}

.post-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #7F8C8D;
}

.post-meta span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.post-content h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2C3E50;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.post-content p {
    color: #7F8C8D;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.read-more {
    color: #F39C12;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.read-more:hover {
    gap: 1rem;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.page-link {
    padding: 0.8rem 1.2rem;
    background: white;
    border: 2px solid #E8E8E8;
    color: #2C3E50;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.page-link.active,
.page-link:hover {
    background: #F39C12;
    color: white;
    border-color: #F39C12;
}

/* Newsletter Section */
.newsletter {
    background: linear-gradient(45deg, #2C3E50, #34495E);
    color: white;
    text-align: center;
}

.newsletter-content h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.newsletter-content p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    gap: 1rem;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
}

.newsletter-form button {
    padding: 1rem 2rem;
    white-space: nowrap;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .blog-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-brand .logo {
        width: 60px;
        height: 60px;
    }

    .gallery-grid {
        grid-template-columns: repeat(1, 1fr);
    }

    .blog-grid {
        grid-template-columns: repeat(1, 1fr);
    }

    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        top: 90px;
        right: -100%;
        width: 100%;
        height: calc(100vh - 50px);
        background: rgba(44, 62, 80);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: all 0.3s ease;
        z-index: 1000;
        backdrop-filter: blur(5px);
    }
    
    .nav-menu.active {
        right: 0;
    }
    
    .nav-menu li {
        margin: .5rem 0;
    }
    
    .slide-title {
        font-size: 2.5rem;
    }
    
    .slide-description {
        font-size: 1.1rem;
    }
    
    .slider-nav {
        top: 85%
    }
    
    .about-content,
    .contact-content,
    .contact-wrapper,
    .service-detail {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .service-detail.reverse {
        direction: rtl;
    }
    
    .stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .portfolio-filter {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .page-header {
        padding: 8rem 0 6rem;
    }
    
    section {
        padding: 3rem 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .slide-title {
        font-size: 2rem;
    }
    
    .slide-description {
        font-size: 1rem;
    }
    
    .slide-content {
        padding: 1rem;
    }
    
    .services-grid,
    .gallery-grid,
    .blog-grid,
    .portfolio-grid,
    .portfolio-grid-extended {
        grid-template-columns: 1fr;
    }
    
    .nav-brand h2 {
        font-size: 1.2rem;
    }
    
    .nav-brand .logo {
        width: 60px;
        height: 60px;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease;
}

.scale-in.visible {
    opacity: 1;
    transform: scale(1);
}

/* Scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.hidden { display: none; }
.visible { display: block; }

/* Modal Base */
.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(45deg, rgba(44, 62, 80, 0.9), rgba(52, 73, 94, 0.85));
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  padding: clamp(15px, 2vw, 20px);
  direction: rtl;
}

.modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  position: relative;
  background: white;
  padding: clamp(20px, 3vw, 30px);
  border-radius: 15px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 5px 15px rgba(243, 156, 18, 0.1);
  max-width: min(90vw, 1000px);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: scale(0.8) translateY(30px);
  transition: all 0.3s ease;
  border: 2px solid rgba(243, 156, 18, 0.1);
}

.modal.active .modal-content {
  transform: scale(1) translateY(0);
}

.modal img {
  max-width: 100%;
  max-height: 70vh;
  transition: transform 0.3s ease;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  object-fit: contain;
}

/* Loading State */
.modal-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #7F8C8D;
}

.modal-loading::after {
  content: '';
  width: 40px;
  height: 40px;
  border: 3px solid #BDC3C7;
  border-top: 3px solid #F39C12;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal {
    padding: 10px;
  }

  .modal-content {
    max-width: 95vw;
    max-height: 95vh;
    padding: 20px 15px;
  }

  .modal-btn {
    font-size: 16px;
    padding: 12px 16px;
    margin: 3px;
  }

  .close-btn {
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .modal img {
    max-height: 60vh;
  }
}

@media (max-width: 480px) {
  .modal-content {
    padding: 15px 10px;
  }

  .modal-controls {
    flex-direction: column;
    gap: 8px;
  }

  .modal-btn {
    width: 100%;
    max-width: 200px;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .modal {
    background: rgba(0, 0, 0, 0.95);
  }

  .modal-content {
    border: 3px solid #F39C12;
  }

  .modal-btn {
    border: 2px solid white;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .modal,
  .modal-content,
  .modal-btn,
  .close-btn,
  .modal img {
    transition: none;
  }

  .modal-loading::after {
    animation: none;
  }
}

/* Dark Mode Support (if implemented) */
@media (prefers-color-scheme: dark) {
  .modal-content {
    background: #2C3E50;
    color: white;
    border-color: #F39C12;
  }

  .modal-title {
    color: white;
  }

  .modal-description {
    color: #BDC3C7;
  }

  .close-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
}
